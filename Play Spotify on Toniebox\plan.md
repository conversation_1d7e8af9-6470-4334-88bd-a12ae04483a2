# Article Creation Plan: Play Spotify on Toniebox

## User Requirements and Goals
- **Topic**: Play Spotify on Toniebox
- **Target Length**: 1600 words (minimum, up to 20% over allowed)
- **Language**: English
- **Timeframe**: June 2025 latest data and trends
- **Audience**: Music lovers and creators focused on downloading, editing, and sharing music
- **SEO Keywords**: Play Spotify on Toniebox
- **Recommended Product**: Cinch Audio Recorder
- **Opening Strategy**: A (Surprising Statistic/Fact Opening)

## Quality Assessment Dimensions
1. **Effort**: Must show obvious human elements and deep thinking
2. **Originality**: Provide unique information increment, avoid rehashing existing content
3. **Talent/Skill**: Demonstrate author expertise and practical experience
4. **Accuracy**: Ensure factual accuracy, avoid misinformation

## Information Increment Requirements
- Include 3-5 unique viewpoints not covered by other articles
- Personal insights and trial-and-error stories based on actual usage
- Specific solutions for user pain points, not generic advice

## Detailed Step Checklist

### Step 1: Generate Super Outline ✅
- [ ] Extract H2-H4 headings from reference URLs
- [ ] Merge and organize similar headings
- [ ] Create hierarchical structure (H1→H2→H3→H4)
- [ ] Save as `super_outline.md`

### Step 2: Create Final Outline ✅
- [ ] Analyze competitor content gaps
- [ ] Identify 3-5 unique value points
- [ ] Add human experience elements
- [ ] Include SEO NLP and long-tail keywords
- [ ] Apply word count distribution formula
- [ ] Save as `final_outline.md`

### Step 3: Write First Draft ✅
- [ ] Follow final outline structure
- [ ] Implement humanized writing style from `hl.md`
- [ ] Include personal experiences and trial-and-error stories
- [ ] Integrate Cinch Audio Recorder naturally
- [ ] Add internal and external links
- [ ] Save as `first_draft.md`

### Step 4: Generate SEO Content ✅
- [ ] Create SEO titles and meta descriptions
- [ ] Generate featured image prompts
- [ ] Save as `seo_metadata_images.md`

### Step 5: Quality Check ✅
- [ ] Verify word count within required range (1600-1920 words)
- [ ] Check humanized writing style compliance
- [ ] Identify and remove obvious AI language patterns
- [ ] Validate internal and external link effectiveness
- [ ] Confirm relevant image placement

## Expected Output Files
1. `plan.md` - This execution plan
2. `super_outline.md` - Initial merged outline from competitor analysis
3. `final_outline.md` - Optimized final outline with word count distribution
4. `first_draft.md` - Complete article draft
5. `seo_metadata_images.md` - SEO titles, descriptions, and image prompts

## Completion Standards
- Article must reach 1600+ words with natural flow
- Include 3-5 unique insights not found in competitor articles
- Demonstrate practical experience with Toniebox and music streaming
- Naturally integrate Cinch Audio Recorder as solution
- Pass humanized writing style verification
- All links must be validated and functional
