# 文章创作执行计划

## 用户需求和目标
基于 `info_aia.md` 文件的要求：

### 文章基本信息
- **主题**: Download Waptrick MP3 Music
- **SEO关键词**: Download Waptrick MP3 Music
- **字数要求**: 1600字（不能少，最多可超出20%，即最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: A (惊人统计/事实开头)

### 内容质量要求
- **Effort (努力程度)**: 体现明显的人工成分和深度思考
- **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
- **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
- **Accuracy (准确性)**: 确保事实准确，避免错误信息

### 信息增量要求
- 包含至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

### 推荐产品
- **Cinch Audio Recorder** ($25.99 USD)
- 官方页面: https://www.cinchsolution.com/cinch-audio-recorder/
- 必须参考 `car_guide.md` 文件内容

## 执行步骤详细清单

### 第1步：基础"超级大纲"生成
1. ✅ 提取参考URL的H2-H4级标题
2. ✅ 合并整理类似标题
3. ✅ 按层级结构重新组织
4. ✅ 保存至 `super_outline.md`

### 第2步：创建最终文章大纲
1. ✅ 竞品内容空白分析
2. ✅ 独特价值点挖掘
3. ✅ 人工经验要素准备
4. ✅ 字数分配计算（目标1600字）
5. ✅ 保存至 `final_outline.md`

### 第3步：创作初稿
1. ⏳ 使用最终大纲撰写初稿
2. ⏳ 确保字数符合要求
3. ⏳ 保存至 `first_draft.md`

### 第4步：生成SEO内容
1. ⏳ 创建SEO标题和元描述
2. ⏳ 生成featured image图片提示词
3. ⏳ 保存至 `seo_metadata_images.md`

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 是否包含至少3个竞品文章未涵盖的独特观点？
- [ ] 是否为每个H2章节准备了人工经验要素？
- [ ] 是否识别并准备解决用户的具体痛点？
- [ ] 是否包含可验证的准确信息和数据？
- [ ] 是否体现了作者的专业判断和建议？
- [ ] 所有章节字数分配总和是否在目标范围内？
- [ ] 核心推荐产品章节是否获得足够字数分配(20-25%)？

### 初稿阶段检查点
- [ ] 字数是否达到1600-1920字范围？
- [ ] 是否正确整合Cinch Audio Recorder产品信息？
- [ ] 是否包含个人经验和试错故事？
- [ ] 是否提供独特的解决方案？

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品信息文件: `New_article/car_guide.md`
- 参考URL: https://www.viwizard.com/record-audio/waptrick-music-download.html
- 大纲工作流程: `New_article/outline.md`
- 初稿工作流程: `New_article/first_draft.md`
- SEO工作流程: `New_article/seo_titles.md`
